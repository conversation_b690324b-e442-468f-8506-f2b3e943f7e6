parso-0.8.4.dist-info/AUTHORS.txt,sha256=SDCgu8hXlBBcjPPyUT-SKW20_IM2MxW-95hKFaRIqyI,2029
parso-0.8.4.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
parso-0.8.4.dist-info/LICENSE.txt,sha256=-meXMHN1PRdiTK-GhNXugW1wyJ2RLFvKfKDwjnsVDts,4176
parso-0.8.4.dist-info/METADATA,sha256=Cddv2ow5rQXI88dE0LdPJiGhqps1pykizCUr-Pi1a64,7674
parso-0.8.4.dist-info/RECORD,,
parso-0.8.4.dist-info/WHEEL,sha256=kGT74LWyRUZrL4VgLh6_g12IeVl_9u9ZVhadrgXZUEY,110
parso-0.8.4.dist-info/top_level.txt,sha256=GOOKQCPcnr0_7IRArxyI0CX5LLu4WLlzIRAVWS-vJ4s,6
parso/__init__.py,sha256=GYriQ4JgyDKw6GeT2fr6iuaIRuVwCekAmlZmnrVnybM,1607
parso/__pycache__/__init__.cpython-39.pyc,,
parso/__pycache__/_compatibility.cpython-39.pyc,,
parso/__pycache__/cache.cpython-39.pyc,,
parso/__pycache__/file_io.cpython-39.pyc,,
parso/__pycache__/grammar.cpython-39.pyc,,
parso/__pycache__/normalizer.cpython-39.pyc,,
parso/__pycache__/parser.cpython-39.pyc,,
parso/__pycache__/tree.cpython-39.pyc,,
parso/__pycache__/utils.cpython-39.pyc,,
parso/_compatibility.py,sha256=y-fATJ1dyaoVry175CMDBA088IGTxChkCKD2dUAnsrU,70
parso/cache.py,sha256=KyQBZdTuBXhDjLmwTSLOgyQoq4NLt_wNr1882DTkOW4,8452
parso/file_io.py,sha256=2SbXQuMpjAaQ0OYvxZXOgl-oU945-CrIei3eEamWWmk,1023
parso/grammar.py,sha256=K7HvV0YV6wcjA-ImfE3hrajXIS0VMcRZkJPaT9-K3rI,10553
parso/normalizer.py,sha256=geYG9UZQ6ZpafTc_CiXQoBt8VImdBsiNw6_GJLeSGbg,5597
parso/parser.py,sha256=qlIrRikSxAccfsC6B6Y9sPWyEhR0HIBaCbNveV1OcAE,7182
parso/pgen2/__init__.py,sha256=kFfRZsSReM49V0YIJ_cG0_TMTew2t4IMbG95KO2BI8E,382
parso/pgen2/__pycache__/__init__.cpython-39.pyc,,
parso/pgen2/__pycache__/generator.cpython-39.pyc,,
parso/pgen2/__pycache__/grammar_parser.cpython-39.pyc,,
parso/pgen2/generator.py,sha256=PHjCpx7QM2duGZqGw5GOQQIgc6RE3jcKV7IwXcOgJhw,14580
parso/pgen2/grammar_parser.py,sha256=knJh3a40_JxUkb0HePG78ZZoqjpPNk3uZwNOz2EkkV4,5515
parso/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
parso/python/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
parso/python/__pycache__/__init__.cpython-39.pyc,,
parso/python/__pycache__/diff.cpython-39.pyc,,
parso/python/__pycache__/errors.cpython-39.pyc,,
parso/python/__pycache__/parser.cpython-39.pyc,,
parso/python/__pycache__/pep8.cpython-39.pyc,,
parso/python/__pycache__/prefix.cpython-39.pyc,,
parso/python/__pycache__/token.cpython-39.pyc,,
parso/python/__pycache__/tokenize.cpython-39.pyc,,
parso/python/__pycache__/tree.cpython-39.pyc,,
parso/python/diff.py,sha256=jyrqWRKklyPPezZRKRHxoKbhkywiCUoGH_K1HcRSyMA,34206
parso/python/errors.py,sha256=Vlmxc0MLUNTYnVNMEWVO68JTpkmXzLEG_sh7rkEt6dA,49113
parso/python/grammar310.txt,sha256=QwXaHqJcJ_zgi9FAAbdv1U_kKgcku9UWjHZoClbtpb4,7511
parso/python/grammar311.txt,sha256=QwXaHqJcJ_zgi9FAAbdv1U_kKgcku9UWjHZoClbtpb4,7511
parso/python/grammar312.txt,sha256=QwXaHqJcJ_zgi9FAAbdv1U_kKgcku9UWjHZoClbtpb4,7511
parso/python/grammar313.txt,sha256=QwXaHqJcJ_zgi9FAAbdv1U_kKgcku9UWjHZoClbtpb4,7511
parso/python/grammar36.txt,sha256=ezjXEeLpG9BBMrN0rbM3Z77mcn0XESxSlAaZEy2er-k,6948
parso/python/grammar37.txt,sha256=Ke73_sTcivtBt2rkJaoNYiXa_zLenhCr96HOVPpZB_E,6804
parso/python/grammar38.txt,sha256=OhPReVYqhsX2RWyVryca3RUGcvLb-R1dcbwdbgPIvBI,7591
parso/python/grammar39.txt,sha256=cVrVbF9Pg5UJLFi2tvLetPkG-BOAkpqDa9hqslNjSHU,7499
parso/python/parser.py,sha256=5OMU32ybPF6kcKUdbcfNNkDOK8hJy0B7fqi6b-Gfwqw,8108
parso/python/pep8.py,sha256=tsuRslXZvfio8LTBIAbfExjBIT1f3Xjx3igt28fm3G4,33779
parso/python/prefix.py,sha256=BM93VenBA1Vs-qk2AJSLBMJNn5BDbyVZLIZ5ScT4FIU,2743
parso/python/token.py,sha256=0dzmQf6L59bEJb9MXYbrDtq3bAHNdTuk-PmOhox81G4,909
parso/python/tokenize.py,sha256=kqmG8SEdkbLG3Gf6gQyeQZerp4yhzzXX14FCYOZJ5mI,25795
parso/python/tree.py,sha256=bwJ54y4Nt_ebUqXiFE7QZbBplNmicL1JDvy-8o-Av7o,37226
parso/tree.py,sha256=deZ68uAq0jodEeumJpBYWXWciIXcBYfpsLpe1f1WLO8,16153
parso/utils.py,sha256=qW8kJuw9pyK8WaIi37FX44kNjAaIgu15EGv7V_R4PmE,6620
