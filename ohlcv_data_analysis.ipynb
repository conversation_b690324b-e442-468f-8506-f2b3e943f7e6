import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import glob
import os

# Set display options for better DataFrame viewing
pd.set_option('display.max_columns', None)
pd.set_option('display.width', None)
pd.set_option('display.max_colwidth', None)

# Find all CSV files in the data directory
data_dir = 'data'
csv_files = glob.glob(os.path.join(data_dir, '*.csv'))

print(f"Found {len(csv_files)} CSV files:")
for file in csv_files:
    print(f"  - {os.path.basename(file)}")

def load_ohlcv_file(file_path):
    """
    Load a single OHLCV CSV file and add a datetime column.
    
    Parameters:
    file_path (str): Path to the CSV file
    
    Returns:
    pd.DataFrame: DataFrame with OHLCV data and datetime column
    """
    # Load the CSV file
    df = pd.read_csv(file_path)
    
    # Convert Unix timestamp to datetime
    df['datetime'] = pd.to_datetime(df['unixTime'], unit='s')
    
    # Add a column for the token symbol (extracted from filename)
    filename = os.path.basename(file_path)
    token_address = filename.split('_')[0]  # Extract address before first underscore
    df['token_symbol'] = token_address
    
    # Reorder columns to put datetime and token_symbol first
    cols = ['datetime', 'token_symbol'] + [col for col in df.columns if col not in ['datetime', 'token_symbol']]
    df = df[cols]
    
    return df

# Load all OHLCV files
dataframes = {}
all_data = []

for file_path in csv_files:
    filename = os.path.basename(file_path)
    token_address = filename.split('_')[0]
    
    print(f"Loading {filename}...")
    df = load_ohlcv_file(file_path)
    
    # Store individual DataFrame
    dataframes[token_address] = df
    
    # Add to combined list
    all_data.append(df)
    
    print(f"  - Loaded {len(df)} rows")
    print(f"  - Date range: {df['datetime'].min()} to {df['datetime'].max()}")
    print()

# Combine all DataFrames
if all_data:
    combined_df = pd.concat(all_data, ignore_index=True)
    
    # Sort by datetime and token
    combined_df = combined_df.sort_values(['datetime', 'token_symbol']).reset_index(drop=True)
    
    print(f"Combined DataFrame shape: {combined_df.shape}")
    print(f"Date range: {combined_df['datetime'].min()} to {combined_df['datetime'].max()}")
    print(f"Unique tokens: {combined_df['token_symbol'].nunique()}")
    print(f"Total records: {len(combined_df)}")
else:
    print("No data files found!")
    combined_df = pd.DataFrame()

# Display basic information about the combined dataset
if not combined_df.empty:
    print("=== COMBINED DATASET INFO ===")
    print(combined_df.info())
    print("\n=== FIRST FEW ROWS ===")
    display(combined_df.head(10))
    
    print("\n=== COLUMN DESCRIPTIONS ===")
    print("datetime: Converted timestamp to readable date/time")
    print("token_symbol: Token address (used as identifier)")
    print("address: Original token address from file")
    print("c: Close price")
    print("h: High price")
    print("l: Low price")
    print("o: Open price")
    print("type: Time interval (1m = 1 minute)")
    print("unixTime: Original Unix timestamp")
    print("v: Volume")

# Display individual token DataFrames
if dataframes:
    print("=== INDIVIDUAL TOKEN DATAFRAMES ===")
    for token_address, df in dataframes.items():
        print(f"\n--- Token: {token_address} ---")
        print(f"Shape: {df.shape}")
        print(f"Date range: {df['datetime'].min()} to {df['datetime'].max()}")
        print("First 3 rows:")
        display(df.head(3))
        
        # Basic price statistics
        print("\nPrice Statistics:")
        price_stats = df[['o', 'h', 'l', 'c']].describe()
        display(price_stats)

if not combined_df.empty:
    print("=== DATA QUALITY CHECK ===")
    
    # Check for missing values
    print("\nMissing values per column:")
    missing_values = combined_df.isnull().sum()
    print(missing_values[missing_values > 0] if missing_values.sum() > 0 else "No missing values found!")
    
    # Check for duplicate timestamps per token
    print("\nDuplicate timestamps per token:")
    duplicates = combined_df.groupby('token_symbol')['datetime'].apply(lambda x: x.duplicated().sum())
    print(duplicates[duplicates > 0] if duplicates.sum() > 0 else "No duplicate timestamps found!")
    
    # Check price consistency (high >= low, etc.)
    print("\nPrice consistency check:")
    price_issues = {
        'high_less_than_low': (combined_df['h'] < combined_df['l']).sum(),
        'close_outside_high_low': ((combined_df['c'] > combined_df['h']) | (combined_df['c'] < combined_df['l'])).sum(),
        'open_outside_high_low': ((combined_df['o'] > combined_df['h']) | (combined_df['o'] < combined_df['l'])).sum(),
        'negative_prices': (combined_df[['o', 'h', 'l', 'c']] < 0).any(axis=1).sum(),
        'zero_volume': (combined_df['v'] == 0).sum()
    }
    
    for issue, count in price_issues.items():
        print(f"{issue}: {count} records")

if not combined_df.empty:
    print("=== SUMMARY STATISTICS BY TOKEN ===")
    
    summary_stats = []
    
    for token in combined_df['token_symbol'].unique():
        token_data = combined_df[combined_df['token_symbol'] == token]
        
        stats = {
            'token': token,
            'records': len(token_data),
            'start_date': token_data['datetime'].min(),
            'end_date': token_data['datetime'].max(),
            'duration_hours': (token_data['datetime'].max() - token_data['datetime'].min()).total_seconds() / 3600,
            'avg_price': token_data['c'].mean(),
            'min_price': token_data['l'].min(),
            'max_price': token_data['h'].max(),
            'total_volume': token_data['v'].sum(),
            'avg_volume': token_data['v'].mean()
        }
        summary_stats.append(stats)
    
    summary_df = pd.DataFrame(summary_stats)
    display(summary_df)